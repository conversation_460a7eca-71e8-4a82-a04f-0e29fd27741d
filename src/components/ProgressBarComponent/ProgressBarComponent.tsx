import {StyleSheet, Text, View} from 'react-native';
import React, {FC} from 'react';
import * as Progress from 'react-native-progress';
import {Fonts, theme} from '../../utilities/theme';
import {useTranslation} from 'react-i18next';

interface Props {
  protenIntake: number;
  proteinTotal: number;
  carbsIntake: number;
  carbsTotal: number;
  fatsIntake: number;
  fatsTotal: number;
}

const ProgressBarComponent: FC<Props> = ({
  carbsIntake,
  carbsTotal,
  fatsIntake,
  fatsTotal,
  proteinTotal,
  protenIntake,
}) => {
  const {t} = useTranslation();

  return (
    <View style={styles.progressBarContainer}>
      {/* PROTEIN */}
      <View style={{flexDirection: 'row'}}>
        <View style={styles.progressDirection}>
          <Progress.Bar
            progress={protenIntake / proteinTotal || 0}
            height={8}
            width={53}
            animated
            unfilledColor="#0000001C"
            color={theme.lightColors?.primary}
            borderColor="transparent"
          />
        </View>
        <View style={{marginLeft: -30}}>
          <Text style={styles.typeText}>{t('Proteins')}</Text>
          <Text style={styles.calText}>
            {protenIntake.toFixed(1) || 0}g /
            <Text style={styles.totalCal}>{proteinTotal}g</Text>
          </Text>
        </View>
      </View>

      {/* CARBS */}
      <View style={{flexDirection: 'row'}}>
        <View style={styles.progressDirection}>
          <Progress.Bar
            progress={carbsIntake / carbsTotal || 0}
            height={8}
            width={53}
            animated
            unfilledColor="#0000001C"
            color={theme.lightColors?.primary}
            borderColor="transparent"
          />
        </View>
        <View style={{marginLeft: -30}}>
          <Text style={styles.typeText}>{t('Carbs')}</Text>
          <Text style={styles.calText}>
            {carbsIntake.toFixed(1) || 0}g /
            <Text style={styles.totalCal}>{carbsTotal}g</Text>
          </Text>
        </View>
      </View>

      {/* FATS */}
      <View style={{flexDirection: 'row'}}>
        <View style={styles.progressDirection}>
          <Progress.Bar
            progress={fatsIntake / fatsTotal || 0}
            height={8}
            width={53}
            animated
            unfilledColor="#0000001C"
            color={theme.lightColors?.primary}
            borderColor="transparent"
          />
        </View>
        <View style={{marginLeft: -30}}>
          <Text style={styles.typeText}>{t('Fats')}</Text>
          <Text style={styles.calText}>
            {fatsIntake.toFixed(1)}g /
            <Text style={styles.totalCal}>{fatsTotal}g</Text>
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ProgressBarComponent;

const styles = StyleSheet.create({
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 36,
  },
  typeText: {
    fontFamily: Fonts.bold,
    fontSize: 14,
    lineHeight: 18,
    color: theme.lightColors?.black,
  },
  calText: {
    color: theme.lightColors?.black,
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    lineHeight: 18,
    marginTop: 8,
  },
  totalCal: {fontFamily: Fonts.regular, fontSize: 12, lineHeight: 15},
  progressDirection: {transform: [{rotate: '-90deg'}]},
});

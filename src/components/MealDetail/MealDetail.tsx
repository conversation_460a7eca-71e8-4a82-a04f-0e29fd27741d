import {Image, StyleSheet, Text, View} from 'react-native';
import React from 'react';
import {TouchableOpacity} from 'react-native';
import PNGIcons from '../../assets/pngIcons';
import {Fonts, theme} from '../../utilities/theme';
import {IFood} from '../../interfaces/IFoods';
import {useTranslation} from 'react-i18next';

interface props {
  onEditPress: () => void;
  onDeletePress: () => void;
  food: IFood;
  mealDetailPress: () => void;
}

const MealDetail: React.FC<props> = ({
  onEditPress,
  onDeletePress,
  food,
  mealDetailPress,
}) => {
  const {t} = useTranslation();

  return (
    <TouchableOpacity onPress={mealDetailPress}>
      <View style={styles.mealContainer}>
        <View style={styles.rowContainer}>
          <View>
            <Text style={styles.mealName}>{food.name}</Text>
            <Text style={styles.descText}>
              {food.calories} {t('cal')}
            </Text>
            <Text style={styles.descText}>
              {food.quantity} {t('serving')}
            </Text>
          </View>
          <View>
            <TouchableOpacity style={styles.editButton} onPress={onEditPress}>
              <Image source={PNGIcons.EditPencil} style={styles.editIcon} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={onDeletePress}>
              <Image source={PNGIcons.CrossIcon} style={styles.editIcon} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.divider} />
        <View style={styles.rowContainer}>
          <Text style={styles.proteinText}>
            {food.proteins}g {t('Proteins')}
          </Text>
          <View style={styles.verticalDivider} />
          <Text style={styles.proteinText}>
            {food.carbs}g {t('Carbs')}
          </Text>
          <View style={styles.verticalDivider} />
          <Text style={styles.proteinText}>
            {food.fats}g {t('Fats')}
          </Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

export default MealDetail;

const styles = StyleSheet.create({
  mealContainer: {
    padding: 12,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    marginTop: 14,
  },
  editIcon: {width: 18, height: 18},
  verticalDivider: {
    borderRightColor: theme.lightColors?.grey2,
    borderRightWidth: 1,
    height: 16,
  },
  proteinText: {
    color: '#2C2C2EB2',
    fontSize: 12,
    fontFamily: Fonts.regular,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  divider: {
    borderTopColor: theme.lightColors?.grey2,
    borderTopWidth: 1,
    marginVertical: 18,
    alignSelf: 'center',
    width: '100%',
  },
  descText: {
    fontFamily: Fonts.regular,
    fontSize: 12,
    color: '#9D9D9D',
    marginTop: 5,
  },
  editButton: {
    padding: 4,
    backgroundColor: theme.lightColors?.grey1,
    width: 26,
    borderRadius: 8,
    marginBottom: 8,
  },
  cancelButton: {
    padding: 4,
    backgroundColor: theme.lightColors?.grey1,
    width: 26,
    borderRadius: 8,
  },
  mealName: {
    fontFamily: Fonts.medium,
    fontSize: 14,
    color: theme.lightColors?.black,
  },
});

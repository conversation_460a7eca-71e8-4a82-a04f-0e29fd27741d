import {
  ActivityIndicator,
  Image,
  Keyboard,
  LayoutAnimation,
  Platform,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  UIManager,
  View,
} from 'react-native';
import React, {useCallback, useEffect, useState} from 'react';
import {Button, FormInput, SearchBar} from '../../../components';
import Modal from 'react-native-modal';
import PNGIcons from '../../../assets/pngIcons';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {styles} from './styles';
import GeneralModal from '../../../components/Modals/GeneralModal/GeneralModal';
import {theme} from '../../../utilities/theme';
import {IFood} from '../../../interfaces/IFoods';
import MealServices from '../../../services/MealServices';
import {IFoodMeal} from '../../../interfaces/IMeal';
import MealDetail from '../../../components/MealDetail/MealDetail';
import {AxiosError} from 'axios';

import {useAppDispatch, useAppSelector} from '../../../store';
import Toast from 'react-native-toast-message';
import FoodServices from '../../../services/FoodServices';
import {addNewFood} from '../../../store/FoodsSlice';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {Input} from '@rneui/base';
import {useFocusEffect} from '@react-navigation/native';
import images from '../../../assets/images';
import {useTranslation} from 'react-i18next';
import {calculateCalories} from '../../../utilities/app.utils';
import useClients from '../../../hooks/models/useClients';
import moment from 'moment';
import useApiHandler from '../../../utilities/useApiHandler';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import useMeals from '../../../hooks/models/useMeals';
import {setMealFoods} from '../../../store/MealsSlice';
import CoachReviewCard from '../../../components/CoachReview/CoachReviewCard';

const validationSchema = Yup.object().shape({
  name: Yup.string().required('Food name is required'),
  quantity: Yup.number().required('Quantity is required'),
  calories: Yup.number().required('Enter calories'),
  proteins: Yup.number().required('Enter proteins'),
  carbs: Yup.number().required('Enter carbs'),
  fats: Yup.number().required('Enter fats'),
});
if (
  Platform.OS === 'android' &&
  UIManager.setLayoutAnimationEnabledExperimental
) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}
type Props = NativeStackScreenProps<ClientStackParamList, 'MealHistory'>;
const MealHistory: React.FC<Props> = ({navigation, route}) => {
  const {mealTitle, date, mealId} = route.params;

  const {t} = useTranslation();
  const dispatch = useAppDispatch();
  const foodsStore = useAppSelector(state => state.foods);
  const {mealFoods} = useAppSelector(state => state.meals);

  const {showErrorToast} = useApiHandler();
  const [isMealLoading, setMealLoading] = useState(true);

  const {mealDetails, getMealDetails, updateMealFoods, createMeal, fetchMeals} =
    useMeals();
  const {fetchClient} = useClients();

  const [isLoading, setLoading] = useState(false);
  // MODAL HANLDERS - ADD NEW FOOD
  const [editFood, setEditFood] = useState<IFood>();
  const [isModalVisible, setModalVisible] = useState(false);
  // MODAL HANDLERS - DELETE FOOD
  const [deleteFoodId, setDeleteFoodId] = useState<undefined | string>();
  const [delLoading, setDelLoading] = useState(false);
  const [deleteFoodModal, setDeleteFoodModal] = useState(false);

  useEffect(() => {
    if (route.params.mealId) {
      findAndSetExistingFoods(route.params.mealId);
    } else {
      // Clear any existing food in redux and set loading to false
      dispatch(setMealFoods([]));
      setMealLoading(false); // Add this line to stop loading when no meal exists
    }
  }, [route.params.mealId]);

  const closeNewFoodModal = () => {
    if (editFood) setEditFood(undefined);
    setModalVisible(false);
    formik.resetForm();
  };

  const handleDeleteFoodShow = (foodId: string) => {
    setDeleteFoodId(foodId);
    setDeleteFoodModal(true);
  };
  const handleDeleteFoodClose = () => setDeleteFoodModal(false);

  // SUBMIT FORM TO ADD FOOD
  async function submitFormToAddNewFood(formData: any) {
    setLoading(true);
    // EDIT & UPDATE FOOD
    if (editFood) {
      try {
        const resp = await FoodServices.UpdateFood(
          editFood?._id as string,
          formData,
        );

        // SUCCESS UPDATE
        if (resp.status === 200) {
          if (editFood) {
            let newSelectedFoodsArr = mealFoods.map(item =>
              item._id === editFood._id
                ? {_id: editFood._id, ...formData}
                : item,
            );
            dispatch(setMealFoods([...newSelectedFoodsArr]));
            await fetchClient();
            closeNewFoodModal();
            Toast.show({
              type: 'success',
              text1: t('Success'),
              text2: t('Food updated successfully.'),
            });
          }
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log('Error creating new food', err.response?.data);
        // INTERNET NOT WORKING
        if (err.code === 'ERR_NETWORK') {
          showErrorToast(t('Make sure your internet is working.'));
        } else {
          showErrorToast(t('Something went wrong.'));
        }
      } finally {
        setLoading(false);
      }
      return;
    }
    // ADD NEW FOOD
    else {
      try {
        const payload = {
          name: formData.name,
          quantity: formData.quantity,
          calories: formData.calories,
          proteins: formData.proteins,
          carbs: formData.carbs,
          fats: formData.fats,
        };
        const resp = await FoodServices.CreateFood(payload);

        // SUCCESS - NEW FOOD ADDED
        if (resp.status == 201) {
          dispatch(setMealFoods([...mealFoods, resp.data]));

          let meal = mealDetails;

          if (!meal?._id) {
            meal = await createMeal(mealTitle);
          }

          if (meal) {
            // updating on backend
            updateMealFoods(meal, [...mealFoods, resp.data]);
          }
          await fetchClient();
          dispatch(addNewFood(resp.data as IFood));
          closeNewFoodModal();
          formik.resetForm();
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Food added successfully.'),
          });
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log('Error creating new food', err);
        // INTERNET NOT WORKING
        if (err.code === 'ERR_NETWORK') {
          showErrorToast(t('Make sure your internet is working.'));
        } else {
          showErrorToast(t('Something went wrong.'));
        }
      } finally {
        setLoading(false);
      }
    }
  }

  // GET FOODS
  async function findAndSetExistingFoods(mealId: string) {
    try {
      setMealLoading(true);
      const mealData = await getMealDetails(mealId);

      if (mealData && mealData.mealFoods) {
        // Filtering foods which are already selected
        const filterSeletedFoods = mealData.mealFoods.filter(foodItem => {
          let foodIsSelected = foodsStore.find(
            item => item._id === foodItem._id,
          );
          if (foodIsSelected) {
            return foodIsSelected;
          }
        });

        dispatch(setMealFoods(filterSeletedFoods));
      } else {
        // If no meal data or no foods, set empty array
        dispatch(setMealFoods([]));
      }
    } catch (_error) {
      console.log('Error getting meal details', _error);
      // Even on error, clear the foods and stop loading
      dispatch(setMealFoods([]));
    } finally {
      setMealLoading(false); // This ensures loading always stops
    }
  }

  // DELETE THE FOOD - API
  async function removeFoodFromMeal(foodId: string) {
    setDelLoading(true);
    const mealFoodsPayload = mealFoods
      .filter(item => item._id !== foodId)
      .map(item => {
        return {foodId: item._id, serving: '1'} as IFoodMeal;
      }) as IFoodMeal[];
    const payload = {
      mealTitle: mealDetails?.mealTitle,
      mealFoods: mealFoodsPayload,
    };
    try {
      const resp = await MealServices.UpdateMeal(
        mealDetails?._id as string,
        payload,
      );

      // SUCCESS - FOOD DELETED
      if (resp.status === 200) {
        await fetchClient();
        await fetchMeals(date);
        const filteredFoods = mealFoods.filter(item => item._id !== foodId);
        dispatch(setMealFoods(filteredFoods));
        Toast.show({
          type: 'success',
          text1: t('Success'),
          text2: t('Food deleted from the meal.'),
        });
        setDelLoading(false);
        handleDeleteFoodClose();
      }
    } catch (error) {
      setDelLoading(false);
      const err = error as AxiosError;
      console.log('- DELETE FOOD -', err.response?.data);
      // INTERNET NOT WOKRING
      if (err.code === 'ERR_NETWOKR') {
        showErrorToast(t('Make sure your internet is working.'));
      } else {
        showErrorToast(t('Somehting went wrong.'));
      }
    }
  }

  // EDIT FOOD HANDLER
  function handlEditFood(food: IFood) {
    setEditFood(food);
    formik.setFieldValue('name', food.name);
    formik.setFieldValue('quantity', food.quantity.toString());
    formik.setFieldValue('calories', food.calories);
    formik.setFieldValue('proteins', food.proteins);
    formik.setFieldValue('carbs', food.carbs);
    formik.setFieldValue('fats', food.fats);
    setModalVisible(true);
  }

  const formik = useFormik({
    initialValues: {
      name: '',
      quantity: 1,
      calories: '',
      proteins: '',
      carbs: '',
      fats: '',
    },
    validationSchema,
    validateOnMount: true,
    onSubmit: submitFormToAddNewFood,
  });
  const {proteins, carbs, fats} = formik.values;

  useEffect(() => {
    let calculatedSum = calculateCalories({
      carbs: Number(carbs),
      proteins: Number(proteins),
      fat: Number(fats),
    });
    formik.setFieldValue('calories', calculatedSum.sum);
  }, [proteins, carbs, fats]);

  return (
    <View style={styles.container}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{paddingBottom: 30}}>
        {!moment(date).isBefore(moment().startOf('day')) ? (
          <TouchableOpacity activeOpacity={0.8}>
            <SearchBar
              micBtn
              onFocus={() => {
                Keyboard.dismiss();
                navigation.navigate('SearchFood', {
                  mealId: mealDetails?._id,
                  mealTitle: route.params.mealTitle,
                  mealFoods: mealDetails?.mealFoods,
                });
              }}
            />
          </TouchableOpacity>
        ) : null}

        <>
          {!moment(date).isBefore(moment().startOf('day')) ? (
            <>
              <View style={styles.rowContainer}>
                {/* <TouchableOpacity
                  style={styles.buttonBackground}
                  onPress={() =>
                    navigation.navigate('ScanCode', {mealId: mealId})
                  }>
                  <Image
                    source={PNGIcons.CameraIcon}
                    style={styles.cameraIcon}
                  />
                </TouchableOpacity> */}
                <TouchableOpacity
                  style={[styles.buttonBackground, {marginHorizontal: 8}]}
                  onPress={() => {
                    navigation.navigate('ScanCode', {mealId: mealId});
                  }}>
                  <Image source={PNGIcons.ScanIcon} style={styles.cameraIcon} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.buttonBackground}
                  onPress={() => setModalVisible(true)}>
                  <Image source={PNGIcons.EditIcon} style={styles.cameraIcon} />
                </TouchableOpacity>
              </View>
              <View style={styles.divider} />
            </>
          ) : null}
          <View style={[styles.rowContainer, {marginTop: 14}]}>
            <Text style={styles.headingText}>
              {!moment(date).isBefore(moment().startOf('day'))
                ? t(`Today's ${route.params.mealTitle}`)
                : t(
                    `${moment(date).format('DD MMM')} ${
                      route.params.mealTitle
                    }`,
                  )}
            </Text>
          </View>

          {/* LIST OF FOOD EATEN IN THE MEAL */}
          {!isMealLoading && mealFoods?.length
            ? mealFoods.map(item => {
                return (
                  <MealDetail
                    onDeletePress={() => {
                      handleDeleteFoodShow(item._id);
                    }}
                    onEditPress={() => handlEditFood(item)}
                    key={item._id}
                    food={item}
                    mealDetailPress={() =>
                      navigation.navigate('FoodDetails', {food: item})
                    }
                  />
                );
              })
            : null}

          {/* REVIEW ON THE MEAL */}
          {mealDetails?.review ? (
            <View style={styles.coachReviewContainer}>
              <View style={styles.coachReviewInnerContainer}>
                <Text style={styles.coachReviewText}>{t('Coach Reviews')}</Text>
                <Image source={PNGIcons.ArrowDown} style={styles.arrowIcon} />
              </View>
              <Text style={{color: theme.lightColors?.grey4}}>
                {mealDetails.review.text}
              </Text>
              {mealDetails.review.isReacted ? (
                <View
                  style={{
                    flexDirection: 'row',
                    alignItems: 'center',
                  }}>
                  <Text style={{marginTop: 4}}>Liked by coach: </Text>
                  <Icon
                    name="thumb-up"
                    size={26}
                    color={theme.lightColors?.searchBg}
                    style={{paddingHorizontal: 8}}
                    suppressHighlighting
                  />
                </View>
              ) : null}
            </View>
          ) : null}

          {/* NO FOOD YET */}
          {!mealFoods.length && !isMealLoading ? (
            <>
              <View style={styles.emptyContainer}>
                <>
                  <Image
                    source={images.noFood}
                    style={styles.noFoodImage}
                    resizeMode="contain"
                  />
                  <Text style={styles.noFoodHeading}>{t('No Food added')}</Text>
                  <Text style={styles.noFoodText}>
                    {t('Please add foods to the meal to see here')}
                  </Text>
                </>
                {!moment(date).isBefore(moment().startOf('day')) ? (
                  <Button
                    title={t('Add Food')}
                    containerStyle={{marginTop: 32, paddingHorizontal: 20}}
                    onPress={() => setModalVisible(true)}
                  />
                ) : null}
              </View>
            </>
          ) : null}

          {/* LOADING FOODS IN THE MEAL */}
          {isMealLoading && (
            <ActivityIndicator
              style={styles.loadingFoods}
              size="large"
              color={theme.lightColors?.primary}
            />
          )}

          {/* COACH REVIEW CARD */}
          {!!mealFoods.length && (
            <CoachReviewCard
              reviewText={t(
                'Lorem ipsum dolor sit amet consectetur. Commodo ac lectus fames ultricies nunc in ultrices. Nibh eu velit diam quis sollicitudin sagittis id et ultrices.',
              )}
            />
          )}
        </>
      </ScrollView>

      {/* ADD NEW FOOD MODAL */}
      <Modal
        isVisible={isModalVisible}
        animationIn={'slideInUp'}
        animationOut={'slideOutDown'}
        animationInTiming={500}
        animationOutTiming={500}
        onBackdropPress={closeNewFoodModal}
        backdropOpacity={0.3}
        avoidKeyboard
        style={{margin: 0, justifyContent: 'flex-end'}}
        propagateSwipe={true}>
        <View style={styles.modalContainer}>
          <Text style={styles.modalHeading}>{t('Add New Food')}</Text>
          <View style={styles.divider} />
          <ScrollView
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{}}>
            <FormInput
              value={formik.values.name}
              label={t('Name of Food')}
              onChangeText={formik.handleChange('name')}
              onBlur={formik.handleBlur('name')}
              errorMessage={
                formik.touched.name
                  ? t(formik.errors.name as string)
                  : undefined
              }
            />

            {/* QUANTITY */}
            {/* <FormInput
              value={formik.values.quantity.toString()}
              label={t('Quantity')}
              onChangeText={formik.handleChange('quantity')}
              onBlur={formik.handleBlur('quantity')}
              errorMessage={
                formik.touched.quantity
                  ? t(formik.errors.quantity as string)
                  : undefined
              }
              keyboardType="numeric"
            /> */}

            {/* CALORIES */}
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Calories')}</Text>
              <Input
                rightIcon={<Text style={styles.unitText}>{t('cal')}</Text>}
                containerStyle={styles.containerStyles}
                inputContainerStyle={styles.inputContainer}
                inputStyle={{color: theme.lightColors?.black}}
                value={formik.values.calories.toString()}
                onChangeText={formik.handleChange('calories')}
                onBlur={formik.handleBlur('calories')}
                keyboardType="numeric"
                errorMessage={
                  formik.touched.calories
                    ? t(formik.errors.calories as string)
                    : undefined
                }
                disabled
              />
            </View>

            {/* PROTEINS */}
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Proteins')}</Text>
              <Input
                rightIcon={<Text style={styles.unitText}>g</Text>}
                containerStyle={styles.containerStyles}
                inputContainerStyle={styles.inputContainer}
                value={formik.values.proteins.toString()}
                inputStyle={{color: theme.lightColors?.black}}
                onChangeText={formik.handleChange('proteins')}
                onBlur={formik.handleBlur('proteins')}
                keyboardType="numeric"
                errorMessage={
                  formik.touched.proteins
                    ? t(formik.errors.proteins as string)
                    : undefined
                }
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Carbs')}</Text>
              <Input
                rightIcon={<Text style={styles.unitText}>g</Text>}
                containerStyle={styles.containerStyles}
                inputStyle={{color: theme.lightColors?.black}}
                inputContainerStyle={styles.inputContainer}
                value={formik.values.carbs.toString()}
                onChangeText={formik.handleChange('carbs')}
                onBlur={formik.handleBlur('carbs')}
                keyboardType="numeric"
                errorMessage={
                  formik.touched.carbs
                    ? t(formik.errors.carbs as string)
                    : undefined
                }
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.inputLabel}>{t('Fats')}</Text>
              <Input
                rightIcon={<Text style={styles.unitText}>g</Text>}
                containerStyle={styles.containerStyles}
                inputContainerStyle={styles.inputContainer}
                inputStyle={{color: theme.lightColors?.black}}
                value={formik.values.fats.toString()}
                onChangeText={formik.handleChange('fats')}
                onBlur={formik.handleBlur('fats')}
                keyboardType="numeric"
                errorMessage={
                  formik.touched.fats
                    ? t(formik.errors.fats as string)
                    : undefined
                }
              />
            </View>
            <View style={styles.addTextContainer}>
              <TouchableOpacity style={styles.textWrapper} activeOpacity={0.7}>
                <Text style={styles.addText}>+ Add more nutrients</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={styles.deleteFoodContainer}
              onPress={closeNewFoodModal}>
              <Text style={styles.deleteFood}>{t('Cancel')}</Text>
            </TouchableOpacity>
            <Button
              loading={isLoading}
              title={t('Add')}
              containerStyle={styles.saveButton}
              onPress={formik.handleSubmit}
              disabled={!formik.isValid || isLoading}
            />
          </View>
        </View>
      </Modal>

      {/* DELETE FOOD MODAL */}
      <GeneralModal
        visible={deleteFoodModal}
        onCancel={handleDeleteFoodClose}
        loading={delLoading}
        disabled={delLoading}
        primaryButtonOnpress={() => removeFoodFromMeal(deleteFoodId as string)}
        topRedTitle={t('Delete Food')}
        description={t('You are attempting to delete food.')}
        primaryButtonName={t('Delete')}
        secondaryButtonText={t('Cancel')}
      />
    </View>
  );
};

export default MealHistory;

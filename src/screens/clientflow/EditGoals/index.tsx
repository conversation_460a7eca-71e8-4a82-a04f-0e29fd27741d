import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import React, {useEffect, useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Button, FormInput} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {useFormik} from 'formik';
import ClientServices, {IClientGoal} from '../../../services/ClientServices';
import Toast from 'react-native-toast-message';
import * as yup from 'yup';
import {calculateCalories} from '../../../utilities/app.utils';
import {AxiosError} from 'axios';
import {Logger} from '../../../utilities/api.handlers';
import {useAppDispatch, useAppSelector} from '../../../store';
import {useTranslation} from 'react-i18next';
import {setDailyGoal} from '../../../store/userSlice';
import useApiHandler from '../../../utilities/useApiHandler';

type Props = NativeStackScreenProps<ClientStackParamList, 'EditGoals'>;

const validationSchema = yup.object().shape({
  calories: yup.string().required('Calories is required'),
  proteins: yup.string().required('Proteins is required'),
  proteinsPercentage: yup.string().required('Proteins % is required'),
  carbs: yup.string().required('Carbs is required'),
  carbsPercentage: yup.string().required('Carbs % is required'),
  fat: yup.string().required('Fats is required'),
  fatPercentage: yup.string().required('Fats % is required'),
});

const EditGoals: React.FC<Props> = ({navigation, route}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const user = useAppSelector(state => state.user.user);
  const {goal} = route.params;
  const [isLoading, setLoading] = useState(false);
  const dispatch = useAppDispatch();

  async function updateClientGoal(formData: IClientGoal) {
    // PAYLOAD
    const goalPayload = {
      calories: formData.calories,
      proteins: formData.proteins,
      proteinsPercentage: formData.proteinsPercentage,
      carbs: formData.carbs,
      carbsPercentage: formData.carbsPercentage,
      fat: formData.fat,
      fatPercentage: formData.fatPercentage,
      client: user._id,
    } as IClientGoal;
    dispatch(
      setDailyGoal({
        proteins: formData.proteins,
        proteinsPercentage: formData.proteinsPercentage,
        carbs: formData.carbs,
        carbsPercentage: formData.carbsPercentage,
        fat: formData.fat,
        fatPercentage: formData.fatPercentage,
        _id: user._id,
        calories: formData.calories,
        client: user._id,
        coach: user.coach?._id,
      }),
    );
    navigation.goBack();

    // UPDATE EXSTING GOAL
    if (goal) {
      try {
        setLoading(true);
        const resp = await ClientServices.UpdateClientGoal(
          goal?._id,
          goalPayload,
        );

        // SUCCESS
        if (resp.status === 200) {
          setLoading(false);
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Goal updated successfully'),
          });
        }
      } catch (error) {
        const err = error as AxiosError;
        Logger('--- CLIENT > UPDATE GOALD ---', err.response?.data);
        setLoading(false);
        Toast.show({
          type: 'error',
          text1: t('Error'),
          text2: t("Couldn't update the goal"),
        });
      }
      return;
    }

    // CREATE NEW GOAL FOR CLIENT
    if (!goal) {
      try {
        setLoading(true);

        const resp = await ClientServices.SetClientGoal(goalPayload);

        // SUCCESS
        if (resp.status === 201) {
          setLoading(false);
          formik.resetForm();
          Toast.show({
            type: 'success',
            text1: t('Success'),
            text2: t('Goal created successfully.'),
          });
          navigation.goBack();
        }
      } catch (error) {
        setLoading(false);
        const err = error as AxiosError;
        Logger('--- CLIENT > SET NEW GOAL ---', err.response?.data);
        handleAxiosErrors(err);
      }
    }
  }

  const formik = useFormik({
    initialValues: {
      calories: goal?.calories || 0,
      proteins: goal?.proteins || 0,
      proteinsPercentage: goal?.proteinsPercentage || 0,
      carbs: goal?.carbs || 0,
      carbsPercentage: goal?.carbsPercentage || 0,
      fat: goal?.fat || 0,
      fatPercentage: goal?.fatPercentage || 0,
      client: goal?.client || '',
    },
    onSubmit: updateClientGoal,
    validationSchema: validationSchema,
    enableReinitialize: true,
  });
  const {proteins, carbs, fat} = formik.values;

  useEffect(() => {
    let calInfo = calculateCalories({proteins, carbs, fat});
    formik.setFieldValue('calories', calInfo.sum);
    formik.setFieldValue('proteinsPercentage', calInfo.proteinsPercentage || 0);
    formik.setFieldValue('carbsPercentage', calInfo.carbsPercentage || 0);
    formik.setFieldValue('fatPercentage', calInfo.fatPercentage || 0);
  }, [carbs, fat, proteins]);

  return (
    <View style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View>
            <Text style={styles.heading}>{t('Set your daily goals')}.</Text>

            {/* CALORIES */}
            <FormInput
              label={t('Calories Goal')}
              unitText="cal."
              value={formik.values.calories.toString()}
              onChangeText={formik.handleChange('calories')}
              onBlur={formik.handleBlur('calories')}
              errorMessage={
                formik.touched.calories
                  ? t(formik.errors.calories as string)
                  : undefined
              }
              disabled
            />

            {/* PROTEINS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Proteins Goal')}
                  unitText="g"
                  value={formik.values.proteins.toString()}
                  onChangeText={formik.handleChange('proteins')}
                  onBlur={formik.handleBlur('proteins')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.proteins
                      ? t(formik.errors.proteins as string)
                      : undefined
                  }
                />
              </View>

              {/* PROTEINS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.proteinsPercentage.toString()}
                  onChangeText={formik.handleChange('proteinsPercentage')}
                  onBlur={formik.handleBlur('proteinsPercentage')}
                  errorMessage={
                    formik.touched.proteinsPercentage
                      ? t(formik.errors.proteinsPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>

            {/* CARBS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Carbs Goal')}
                  unitText="g"
                  value={formik.values.carbs.toString()}
                  onChangeText={formik.handleChange('carbs')}
                  onBlur={formik.handleBlur('carbs')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.carbs
                      ? t(formik.errors.carbs as string)
                      : undefined
                  }
                />
              </View>

              {/* CARBS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.carbsPercentage.toString()}
                  onChangeText={formik.handleChange('carbsPercentage')}
                  onBlur={formik.handleBlur('carbsPercentage')}
                  errorMessage={
                    formik.touched.carbsPercentage
                      ? t(formik.errors.carbsPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>

            {/* FATS */}
            <View style={styles.goalInputs}>
              <View style={{flex: 1}}>
                <FormInput
                  label={t('Fats Goal')}
                  unitText="g"
                  value={formik.values.fat.toString()}
                  onChangeText={formik.handleChange('fat')}
                  onBlur={formik.handleBlur('fat')}
                  keyboardType="numeric"
                  errorMessage={
                    formik.touched.fat
                      ? t(formik.errors.fat as string)
                      : undefined
                  }
                />
              </View>

              {/* FATS PERCENTAGE */}
              <View style={{flex: 1, paddingLeft: 15}}>
                <FormInput
                  label=" "
                  unitText="%"
                  value={formik.values.fatPercentage.toString() || '0'}
                  onChangeText={formik.handleChange('fatPercentage')}
                  onBlur={formik.handleBlur('fatPercentage')}
                  errorMessage={
                    formik.touched.fatPercentage
                      ? t(formik.errors.fatPercentage as string)
                      : undefined
                  }
                  disabled
                />
              </View>
            </View>
          </View>

          <Button
            title={t('Save Changes')}
            containerStyle={styles.buttonContainer}
            onPress={formik.handleSubmit}
            disabled={isLoading}
            loading={isLoading}
          />
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
};

export default EditGoals;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
    justifyContent: 'space-between',
  },
  heading: {
    marginTop: 35,
    marginBottom: 24,
    fontFamily: Fonts.semiBold,
    fontSize: 14,
    letterSpacing: 0.7,
    color: theme.lightColors?.secondary,
  },
  goalInputs: {flexDirection: 'row', alignItems: 'center'},
  buttonContainer: {
    flex: 1,
    marginTop: 120,
  },
});

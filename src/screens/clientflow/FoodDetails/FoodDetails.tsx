import {NativeStackScreenProps} from '@react-navigation/native-stack';
import React, {FC, useRef} from 'react';
import {StyleSheet, Text} from 'react-native';
import {View} from 'react-native';
import {ClientStackParamList} from '../../../navigation/ClientStackNavigator/ClientStackNavigator';
import {Fonts, theme} from '../../../utilities/theme';
import {CircularProgressBase} from 'react-native-circular-progress-indicator';
import {FormInput} from '../../../components';
import {ChevronDown} from '../../../assets/svgIcons';
import {useTranslation} from 'react-i18next';

type Props = NativeStackScreenProps<ClientStackParamList, 'FoodDetails'>;

const FoodDetails: FC<Props> = ({route}) => {
  const food = route.params.food;
  const circularProgressRef = useRef(null);
  const {t} = useTranslation();

  return (
    <View style={styles.container}>
      <View style={{marginVertical: 16}}>
        <FormInput
          value={food?.name}
          editable={false}
          rightIcon={<ChevronDown width={20} height={20} />}
          containerStyle={{
            borderColor: theme.lightColors?.primary,
            marginBottom: -20,
          }}
          inputStyle={{borderColor: theme.lightColors?.primary}}
        />
        <View style={{flexDirection: 'row', height: 50}}>
          <View style={{flex: 1, marginRight: 14}}>
            <FormInput
              value={food?.quantity?.toString()}
              editable={false}
              containerStyle={{borderColor: theme.lightColors?.primary}}
              inputStyle={{
                borderColor: theme.lightColors?.primary,
                textAlign: 'center',
              }}
            />
          </View>
          <View style={{flex: 2}}>
            <FormInput
              value={'Serving'}
              editable={false}
              containerStyle={{borderColor: theme.lightColors?.primary}}
              inputStyle={{borderColor: theme.lightColors?.primary}}
            />
          </View>
        </View>
      </View>

      {/* NUTRITIONAL INFORMATION */}
      <Text style={styles.textHeading}>{t('Nutritional Information')}</Text>
      <View style={styles.roundedBox}>
        <Text style={[styles.caloriesCount, styles.textPrimary]}>
          {food?.calories} {t('cal')}
        </Text>

        <View style={styles.graphContainer}>
          {/* Proteins */}
          <View>
            <CircularProgressBase
              initialValue={1}
              ref={circularProgressRef}
              activeStrokeWidth={6}
              inActiveStrokeWidth={8}
              inActiveStrokeOpacity={0.2}
              value={(food?.proteins / food?.calories) * 100 || 0}
              radius={40}
              activeStrokeColor={theme.lightColors?.primary}
              inActiveStrokeColor={theme.lightColors?.primary}
              strokeLinecap={'round'}
              maxValue={100}>
              <Text style={styles.ciclePercentageText}>
                {Math.trunc((food?.proteins / food?.calories) * 100)}%
              </Text>
            </CircularProgressBase>
            <Text style={[styles.graphLabelText, styles.textPrimary]}>
              {t('Proteins')}
            </Text>
          </View>

          {/* CARBS */}
          <View>
            <CircularProgressBase
              initialValue={1}
              ref={circularProgressRef}
              activeStrokeWidth={6}
              inActiveStrokeWidth={8}
              inActiveStrokeOpacity={0.2}
              value={(food?.carbs / food?.calories) * 100}
              radius={40}
              activeStrokeColor={theme.lightColors?.primary}
              inActiveStrokeColor={theme.lightColors?.primary}
              strokeLinecap={'round'}
              maxValue={100}>
              <Text style={styles.ciclePercentageText}>
                {Math.trunc((food?.carbs / food?.calories) * 100)}%
              </Text>
            </CircularProgressBase>
            <Text style={[styles.graphLabelText, styles.textInfo]}>
              {t('Carbs')}
            </Text>
          </View>

          {/* FATS */}
          <View>
            <CircularProgressBase
              initialValue={1}
              ref={circularProgressRef}
              activeStrokeWidth={6}
              inActiveStrokeWidth={8}
              inActiveStrokeOpacity={0.2}
              value={(food?.fats / food?.calories) * 100}
              radius={40}
              activeStrokeColor={theme.lightColors?.primary}
              inActiveStrokeColor={theme.lightColors?.primary}
              strokeLinecap={'round'}
              maxValue={100}>
              <Text style={styles.ciclePercentageText}>
                {Math.trunc((food?.fats / food?.calories) * 100)}%
              </Text>
            </CircularProgressBase>
            <Text style={[styles.graphLabelText, styles.textSuccess]}>
              {t('Fats')}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

export default FoodDetails;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    flex: 1,
  },
  roundedBox: {
    borderColor: theme.lightColors?.grey2,
    borderWidth: 1,
    borderRadius: 14,
    marginVertical: 10,
    padding: 14,
  },
  textHeading: {
    color: theme.lightColors?.black,
    fontFamily: Fonts.bold,
    fontWeight: '600',
    fontSize: 14,
    marginTop: 10,
  },
  caloriesCount: {
    fontSize: 16,
    textAlign: 'center',
    fontFamily: Fonts.semiBold,
  },
  textPrimary: {
    color: theme.lightColors?.primary,
  },
  textInfo: {
    color: theme.lightColors?.primary,
  },
  textSuccess: {
    color: theme.lightColors?.primary,
  },
  graphContainer: {
    marginTop: 12,
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  ciclePercentageText: {
    color: theme.lightColors?.black,
    fontWeight: '600',
    fontSize: 14,
    fontFamily: Fonts.bold,
  },
  graphLabelText: {
    fontWeight: '600',
    fontSize: 14,
    fontFamily: Fonts.bold,
    textAlign: 'center',
    marginTop: 6,
  },
});

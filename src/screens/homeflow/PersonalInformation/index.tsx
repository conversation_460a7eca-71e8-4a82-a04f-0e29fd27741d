import {
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import React, {useState} from 'react';
import {Fonts, theme} from '../../../utilities/theme';
import {Avatar, Button, FormInput} from '../../../components';
import {NativeStackScreenProps} from '@react-navigation/native-stack';
import {HomeStackParamList} from '../../../navigation/HomeStack/HomeStackNavigator';
import {useFormik} from 'formik';
import * as Yup from 'yup';
import {useAppDispatch, useAppSelector} from '../../../store';
import ImagePicker, {ImageOrVideo} from 'react-native-image-crop-picker';
import AuthServices from '../../../services/AuthServices';
import {AxiosError} from 'axios';
import {updateUser} from '../../../store/userSlice';
import {useTranslation} from 'react-i18next';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import moment from 'moment';
import useApiHandler from '../../../utilities/useApiHandler';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

type Props = NativeStackScreenProps<HomeStackParamList, 'PersonalInfo'>;

const Services = new AuthServices();

const PersonalInfo: React.FC<Props> = ({navigation}) => {
  const {t} = useTranslation();
  const {handleAxiosErrors} = useApiHandler();

  const validationSchema = Yup.object().shape({
    name: Yup.string().required(t('Name is required')),
    email: Yup.string()
      .email(t('Invalid email'))
      .required(t('Email is required')),
    profilePicture: Yup.string(),
    dob: Yup.string().required(t('Age is required')),
    experience: Yup.string().required(t('Experience is required')),
    about: Yup.string().required(t('About is required')),
  });

  const userData = useAppSelector(state => state.user.user);
  const dispatch = useAppDispatch();
  const [isDatePickerVisible, setIsDatePickerVisible] = useState(false);
  const [imageDetail, setImageDetail] = useState<ImageOrVideo>();
  const [isLoading, setIsLoading] = useState(false);

  const handleChangeImage = async () => {
    try {
      const image = await ImagePicker.openPicker({
        width: 300,
        height: 400,
        cropping: false,
        mediaType: 'photo',
      });
      formik.setFieldValue('profilePicture', image.path);
      setImageDetail(image);
    } catch (error) {
      console.log('Error selecting image', error);
    }
  };

  const handleUploadImage = async () => {
    if (imageDetail) {
      try {
        const formData = new FormData();
        formData.append('file', {
          uri: imageDetail?.path,
          type: imageDetail?.mime,
          name: userData._id,
        });

        const resp = await Services.UploadPicture(formData);
        if (resp.status == 201) {
          return resp.data;
        }
      } catch (error) {
        const err = error as AxiosError;
        console.log('Error uploading image', err.response);
      }
    }
  };
  const handleSaveChanges = async () => {
    const {name, email, about, dob, experience} = formik.values;
    setIsLoading(true);
    const imageUploaded = await handleUploadImage();
    const image = imageUploaded ? imageUploaded.value : userData?.photo;
    const payload = {
      name,
      email,
      about,
      dob,
      experience,
      photo: image,
    };
    dispatch(
      updateUser({
        name,
        email,
        about,
        dob,
        experience,
        photo: imageUploaded?.url || userData?.photo,
      }),
    );
    navigation.goBack();
    try {
      await Services.CoachUpdate(payload, userData._id);
    } catch (error) {
      const err = error as AxiosError;
      console.log('Error updating data', err.request);
      handleAxiosErrors(err);
    } finally {
      setIsLoading(false);
    }
  };

  const formik = useFormik({
    initialValues: {
      name: userData.name,
      email: userData.email,
      dob: moment(userData.dob).format('YYYY-MM-DD'),
      experience: userData.experience,
      about: userData.about,
      profilePicture: '',
    },
    validateOnMount: true,
    validationSchema: validationSchema,
    onSubmit: handleSaveChanges,
  });

  return (
    <View style={styles.container}>
      <KeyboardAwareScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContainer}>
        <View style={styles.avatarContainer}>
          <Avatar
            uri={
              formik.values.profilePicture
                ? formik?.values?.profilePicture
                : userData?.photo
                ? userData.photo
                : ''
            }
            onPress={handleChangeImage}
            showPickImage
          />
        </View>
        <FormInput
          label={t('Name')}
          editIcon
          labelStyle={styles.nameLabel}
          inputStyle={{color: 'black'}}
          value={formik.values.name}
          onChangeText={formik.handleChange('name')}
          errorMessage={
            formik.touched.name ? t(formik.errors.name as string) : undefined
          }
        />
        <FormInput
          label={t('Email')}
          value={formik.values.email}
          onChangeText={formik.handleChange('email')}
          errorMessage={
            formik.touched.email ? t(formik.errors.email as string) : undefined
          }
          disabled
        />
        {/* <FormInput
          label={t('Age')}
          editIcon
          value={formik.values.dob.toString()}
          onChangeText={formik.handleChange('dob')}
          errorMessage={
            formik.touched.dob ? t(formik.errors.dob as string) : undefined
          }
          keyboardType="numeric"
        /> */}

        <Text style={styles.dobText}>{t('Date of birth')}</Text>
        <TouchableOpacity
          style={styles.datePickerButton}
          onPress={() => setIsDatePickerVisible(true)}>
          <Text
            style={{color: theme.lightColors?.black, fontFamily: Fonts.medium}}>
            {!formik.values?.dob
              ? t('Pick Date')
              : moment(formik.values?.dob).format('MMMM Do, YYYY')}
          </Text>
        </TouchableOpacity>

        <FormInput
          label={t('Experience (years)')}
          editIcon
          value={formik.values.experience}
          onChangeText={formik.handleChange('experience')}
          errorMessage={
            formik.touched.experience
              ? t(formik.errors.experience as string)
              : undefined
          }
          keyboardType="number-pad"
        />
        <FormInput
          label={t('About')}
          editIcon
          value={formik.values.about}
          onChangeText={formik.handleChange('about')}
          errorMessage={
            formik.touched.about ? t(formik.errors.about as string) : undefined
          }
          textinputStyles={styles.aboutInput}
          multiline
          textAlignVertical="top"
        />
        <Button
          title={t('Save Changes')}
          onPress={formik.handleSubmit}
          disabled={!formik.isValid || isLoading}
          loading={isLoading}
        />
      </KeyboardAwareScrollView>
      <DateTimePickerModal
        isVisible={isDatePickerVisible}
        date={moment(formik.values.dob).toDate()}
        maximumDate={new Date()}
        mode="date"
        onConfirm={date => {
          formik.setFieldValue('dob', moment(date).format('YYYY-MM-DD'));
          setIsDatePickerVisible(false);
        }}
        onCancel={() => setIsDatePickerVisible(false)}
      />
    </View>
  );
};

export default PersonalInfo;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.lightColors?.background,
    paddingHorizontal: 24,
  },
  datePickerButton: {
    backgroundColor: theme.lightColors?.grey5,
    borderColor: theme.lightColors?.grey1,
    borderWidth: 1,
    borderRadius: 10,
    padding: 14,
    marginBottom: 14,
  },
  aboutInput: {height: 144, paddingTop: 10},
  dobText: {
    color: theme.lightColors?.black,
    fontFamily: Fonts.light,
    marginBottom: 8,
  },
  nameLabel: {marginTop: 48},
  avatarContainer: {marginTop: 31},
  scrollContainer: {paddingBottom: 30},
});

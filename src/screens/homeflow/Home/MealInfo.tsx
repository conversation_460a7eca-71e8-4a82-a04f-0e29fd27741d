import {Image, StyleSheet, Text, View} from 'react-native';
import React, {FC} from 'react';
import {Divider} from '@rneui/base';
import {Fonts, theme} from '../../../utilities/theme';
import PNGIcons from '../../../assets/pngIcons';
import {Check} from '../../../assets/svgIcons';
import Nutritioninfo from './Nutritioninfo';
import {IMealWithFoods} from '../../../interfaces/IMeal';
import {useTranslation} from 'react-i18next';

interface Props {
  meal: IMealWithFoods;
}

const MealInfo: FC<Props> = ({meal}) => {
  const {t} = useTranslation();
  return (
    <>
      {/* BREAK FAST TITLE */}
      <View style={[styles.breakFastTitleContainer, {marginTop: 14}]}>
        <Image source={PNGIcons.TeaCups} style={styles.iconContainer} />
        <Text style={styles.breakfastText}>{meal.mealTitle}</Text>
      </View>
      {/* DETAILS  TEXT*/}
      <Text style={styles.detailsText}>{t('Details')}</Text>

      {/* LIST OF FOOD IN A MEAL */}
      {meal?.mealFoods.length
        ? meal?.mealFoods?.map(item => {
            return (
              <View style={[styles.breakFastTitleContainer, {marginTop: 6}]}>
                <Check width={14} height={14} />
                <Text style={styles.itemText}>{item.name}</Text>
              </View>
            );
          })
        : null}
      {/* DIVIDER */}
      {/* <Divider style={{marginTop: 12}} color={theme.lightColors?.grey2} /> */}
      {/* CALGORIES */}
      <View style={styles.calgoriesContainer}>
        <Text style={styles.totalText}>
          {' '}
          {/* Total: <Text style={styles.totalText2}>{client.calories} cal</Text> */}
        </Text>
      </View>
      {/* NUTRITION INFORMATION */}
      <View style={styles.nutritionContainer}>
        <Nutritioninfo
          nutritionLabel={t('Proteins')}
          // weight={`${client.proteins} g`}
          weight={`0 g`}
        />
        <Nutritioninfo
          nutritionLabel={t('Carbs')}
          weight={`0 g`}
          // weight={`${client.carbs} g`}
          color={theme.lightColors?.searchBg}
        />
        <Nutritioninfo
          nutritionLabel={t('Fats')}
          // weight={`${client.fats} g`}
          weight={`0 g`}
          color="#58DF42"
        />
      </View>
      {/* ADD REVIEW BUTTONS */}
      {/* <Button
            title="+ Add Reviews"
            containerStyle={styles.button}
            onPress={onAddReviewPress}
            titleStyle={styles.titleStyle}
          /> */}
      <Divider style={{marginTop: 12}} color={theme.lightColors?.grey2} />
    </>
  );
};

export default MealInfo;

const styles = StyleSheet.create({
  container: {
    backgroundColor: theme.lightColors?.white,
    borderWidth: 1,
    borderColor: theme.lightColors?.grey1,
    borderRadius: 14,
    padding: 12,
    marginBottom: 12,
  },
  name: {
    fontSize: 14,
    color: theme.lightColors?.secondary,
    fontFamily: Fonts.semiBold,
  },
  dateText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
  },
  profileInfo: {
    paddingHorizontal: 0,
    paddingVertical: 0,
  },
  breakFastTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 20,
    height: 20,
  },
  breakfastText: {
    fontSize: 14,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
    marginTop: 2,
  },
  detailsText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    marginTop: 12,
  },
  itemText: {
    fontSize: 14,
    fontFamily: Fonts.medium,
    color: theme.lightColors?.secondary,
    paddingLeft: 6,
  },
  calgoriesContainer: {},
  totalText: {
    fontSize: 12,
    color: `${theme.lightColors?.secondary}80`,
    fontFamily: Fonts.regular,
    textAlign: 'center',
    paddingVertical: 12,
  },
  totalText2: {
    fontSize: 16,
    fontFamily: Fonts.semiBold,
    color: theme.lightColors?.primary,
  },
  nutritionContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    // backgroundColor: 'red',
    paddingHorizontal: 32,
  },
  button: {
    width: 120,
    height: 30,
    paddingVertical: 0,
    borderRadius: 6,
    alignSelf: 'flex-end',
    marginTop: 20,
    backgroundColor: `${theme.lightColors?.primary}20`,
  },
  titleStyle: {
    fontSize: 14,
    color: theme.lightColors?.primary,
    fontFamily: Fonts.regular,
  },
});
